<script setup>
import {ref, reactive, onMounted, h} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {useMessage} from 'naive-ui'
import {
  NCard,
  NButton,
  NSpace,
  NDescriptions,
  NDescriptionsItem,
  NTag,
  NTable,
  NCheckbox,
  NModal,
  NForm,
  NFormItem,
  NSelect,
  NInput,
  NPopconfirm,
  NPopover,
  NTooltip,
  NTransfer,
  NLayout,
  NLayoutSider,
  NLayoutContent,
  NGrid,
  NGridItem,
  NTabs,
  NTabPane,
  NSpin,
  NSwitch,
  NRadioGroup,
  NRadio,
  NCheckboxGroup,
  NInputNumber
} from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import TheIcon from '@/components/icon/TheIcon.vue'
import CrudModal from '@/components/table/CrudModal.vue'
import TestReport from '@/components/test-report/TestReport.vue'
import {formatDate} from '@/utils'
import apiTestPlanApi from '@/api/apiTestPlan'
import apiTestCaseApi from '@/api/apiTestCase'
import projectApi from '@/api/project'
import scheduledTaskApi from '@/api/scheduledTask'

defineOptions({name: '接口测试计划详情'})

const route = useRoute()
const router = useRouter()
const $message = useMessage()

const planId = ref(route.params.id)
const planDetail = ref({})
const planCases = ref([])
const loading = ref(false)
const batchExecutionLoading = ref(false)
const testReportVisible = ref(false)
const testReportData = ref(null)
const enableDingtalk = ref(false) // 钉钉通知开关

// 定时任务相关
const scheduledTaskVisible = ref(false)
const scheduledTaskForm = ref({
  task_name: '',
  cron_expression: '',
  description: ''
})
const scheduledTaskLoading = ref(false)
const existingScheduledTask = ref(null) // 存储已存在的定时任务

// Cron表达式配置相关
const cronConfig = ref({
  type: 'daily', // daily, weekly, monthly, custom
  time: '09:00', // 使用字符串格式
  weekdays: [1, 2, 3, 4, 5], // 1-7 代表周一到周日
  dayOfMonth: 1,
  interval: 1
})

const cronTypeOptions = [
  { label: '每分钟', value: 'minutely' },
  { label: '每小时', value: 'hourly' },
  { label: '每天', value: 'daily' },
  { label: '每周', value: 'weekly' },
  { label: '每月', value: 'monthly' },
  { label: '自定义', value: 'custom' }
]

const weekdayOptions = [
  { label: '周一', value: 1 },
  { label: '周二', value: 2 },
  { label: '周三', value: 3 },
  { label: '周四', value: 4 },
  { label: '周五', value: 5 },
  { label: '周六', value: 6 },
  { label: '周日', value: 7 }
]

// 时间选项（每30分钟一个选项）
const timeOptions = []
for (let hour = 0; hour < 24; hour++) {
  for (let minute = 0; minute < 60; minute += 30) {
    const timeStr = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
    timeOptions.push({
      label: timeStr,
      value: timeStr
    })
  }
}

// 添加用例相关
const showAddCaseModal = ref(false)
const availableCases = ref([])
const filteredAvailableCases = ref([])
const selectedCaseIds = ref([])
const addCaseLoading = ref(false)
const searchCaseName = ref('')

// 编辑用例相关
const showEditCaseModal = ref(false)
const editCaseLoading = ref(false)
const editCaseForm = ref({
  id: null,
  case_name: '',
  method: 'GET',
  url: '',
  params: '',
  body: '',
  expected_result: '',
  variable_extracts: '',
  is_smoke: false,
  status: 'pending',
  source: 'manual',
  project_id: '',
  module_id: null,
  assertions: [],
  variableExtracts: []
})

// 选项数据
const methodOptions = ref([
  {label: 'GET', value: 'GET'},
  {label: 'POST', value: 'POST'},
  {label: 'PUT', value: 'PUT'},
  {label: 'DELETE', value: 'DELETE'},
  {label: 'PATCH', value: 'PATCH'}
])

const statusOptions = ref([
  {label: '待审核', value: 'pending'},
  {label: '已审核', value: 'approved'}
])

const moduleOptions = ref([])

// 执行结果弹窗相关状态
const executionModalVisible = ref(false)
const executionLoading = ref(false)
const executionResult = ref(null)

// 环境相关状态
const environmentOptions = ref([])
const selectedEnvironment = ref(null)
const selectedEnvironmentName = ref('选择环境')

// 获取测试计划详情
const getPlanDetail = async () => {
  try {
    loading.value = true
    const {data} = await apiTestPlanApi.getApiTestPlan({plan_id: planId.value})
    planDetail.value = data
    // 同步钉钉开关状态
    enableDingtalk.value = data.enable_dingtalk || false
  } catch (error) {
    $message.error('获取测试计划详情失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 获取测试计划的定时任务
const getScheduledTask = async () => {
  try {
    const response = await scheduledTaskApi.getTasksByPlan({plan_id: planId.value})
    if (response.code === 200 && response.data && response.data.length > 0) {
      existingScheduledTask.value = response.data[0] // 取第一个定时任务
    } else {
      existingScheduledTask.value = null
    }
  } catch (error) {
    console.error('获取定时任务失败:', error)
    existingScheduledTask.value = null
  }
}

// 获取测试计划关联的用例
const getPlanCases = async () => {
  try {
    console.log('=== 开始获取测试计划关联的用例 ===')
    console.log('测试计划ID:', planId.value)

    const response = await apiTestPlanApi.getTestPlanCases({plan_id: planId.value})
    console.log('获取关联用例API响应:', response)
    console.log('关联用例数据:', response.data)

    const cases = response.data || []
    console.log('解析后的关联用例:', cases)
    console.log('关联用例数量:', cases.length)

    if (cases.length > 0) {
      console.log('第一个关联用例示例:', cases[0])
    }

    planCases.value = cases
    console.log('设置后的planCases.value:', planCases.value)

  } catch (error) {
    console.error('=== 获取测试用例失败 ===')
    console.error('错误详情:', error)
    $message.error('获取测试用例失败')
  }
}

// 获取可添加的测试用例（已审核状态）
const getAvailableCases = async () => {
  if (!planDetail.value || !planDetail.value.project_id) {
    console.error('项目ID不存在，无法获取测试用例')
    throw new Error('项目ID不存在')
  }

  try {
    console.log('=== 开始获取已审核测试用例 ===')
    console.log('项目ID:', planDetail.value.project_id)

    const response = await apiTestPlanApi.getApprovedTestCases({
      project_id: planDetail.value.project_id
    })

    console.log('API响应:', response)
    console.log('响应数据:', response.data)

    const cases = response.data || []
    console.log('解析后的用例数据:', cases)
    console.log('用例数量:', cases.length)

    if (cases.length > 0) {
      console.log('第一个用例示例:', cases[0])
    }

    availableCases.value = cases
    console.log('设置后的availableCases.value:', availableCases.value)

    return cases
  } catch (error) {
    console.error('=== 获取测试用例失败 ===')
    console.error('错误详情:', error)
    console.error('错误响应:', error.response)
    $message.error('获取可用测试用例失败: ' + (error.message || '未知错误'))
    throw error
  }
}

// 状态映射
const statusMap = {
  'not_started': {text: '未开始', type: 'default'},
  'in_progress': {text: '进行中', type: 'info'},
  'completed': {text: '已完成', type: 'success'}
}

const levelMap = {
  'high': {text: '高', type: 'error'},
  'medium': {text: '中', type: 'warning'},
  'low': {text: '低', type: 'info'}
}

// 执行状态相关函数
const getExecutionStatusType = (status) => {
  const statusMap = {
    'normal': 'success',
    'skipped': 'default'
  }
  return statusMap[status] || 'default'
}

const getExecutionStatusText = (status) => {
  const statusMap = {
    'normal': '正常',
    'skipped': '跳过'
  }
  return statusMap[status] || status
}

const executionStatusOptions = [
  {label: '正常', value: 'normal'},
  {label: '跳过', value: 'skipped'}
]

// 表格列配置
const caseColumns = [
  {
    title: '执行顺序',
    key: 'execution_order',
    width: 80,
    align: 'center',
    render: (row) => h('span', {style: 'font-weight: 500; color: #1890ff;'}, row.execution_order || 0)
  },
  {
    title: '用例编号',
    key: 'case_number',
    width: 100,
    ellipsis: {tooltip: true}
  },
  {
    title: '用例名称',
    key: 'case_name',
    width: 100,
    ellipsis: {tooltip: true},
    render: (row) => {
      // 根据执行结果决定字体颜色
      const isFailedCase = row.execution_result === 'failed'
      const textColor = isFailedCase ? '#ff4d4f' : '#1890ff'

      return h('span', {
        style: `cursor: pointer; color: ${textColor};`,
        onClick: () => {
          console.log('单击用例名称:', row)
          handleEditCase(row)
        }
      }, row.case_name)
    }
  },
  {
    title: '请求方式',
    key: 'method',
    width: 100,
    align: 'center',
    render: (row) => h(NTag, {
      size: 'tiny',
      type: row.method === 'GET' ? 'success' : row.method === 'POST' ? 'info' : 'warning'
    }, {default: () => row.method})
  },
  {
    title: 'URL',
    key: 'url',
    width: 100,
    ellipsis: {tooltip: true}
  },
  // {
  //   title: '是否冒烟',
  //   key: 'is_smoke',
  //   width: 80,
  //   align: 'center',
  //   render: (row) => h(NTag, {
  //     type: row.is_smoke ? 'success' : 'default',
  //     size: 'small'
  //   }, {default: () => row.is_smoke ? '是' : '否'})
  // },
  {
    title: '执行状态',
    key: 'execution_status',
    width: 70,
    render: (row) => h(NPopover, {
      trigger: 'click',
      placement: 'bottom'
    }, {
      trigger: () => h(NTag, {
        type: getExecutionStatusType(row.execution_status || 'normal'),
        size: 'small',
        style: 'cursor: pointer; user-select: none;',
        bordered: false
      }, {
        default: () => getExecutionStatusText(row.execution_status || 'normal')
      }),
      default: () => h('div', {
        style: 'padding: 8px;'
      }, [
        h('div', {
          style: 'margin-bottom: 8px; font-weight: 500; color: #666;'
        }, '选择执行状态:'),
        h(NSpace, {
          vertical: true,
          size: 'small'
        }, {
          default: () => executionStatusOptions.map(option =>
            h(NTag, {
              type: getExecutionStatusType(option.value),
              size: 'small',
              style: 'cursor: pointer; width: 80px; text-align: center;',
              bordered: row.execution_status === option.value,
              onClick: () => handleUpdateCaseExecutionStatus(row.id, option.value)
            }, {
              default: () => option.label
            })
          )
        })
      ])
    })
  },
  {
    title: '执行结果',
    key: 'execution_result',
    width: 80,
    align: 'center',
    render: (row) => {
      if (!row.execution_result) {
        return h('span', {style: 'color: #999;'}, '未执行')
      }
      return h(NTag, {
        type: row.execution_result === 'success' ? 'success' : 'error',
        size: 'small'
      }, {default: () => row.execution_result === 'success' ? '成功' : '失败'})
    }
  },
  // {
  //   title: '状态码',
  //   key: 'status_code',
  //   width: 100,
  //   align: 'center',
  //   render: (row) => {
  //     if (!row.status_code) {
  //       return h('span', {style: 'color: #999;'}, '-')
  //     }
  //     return h(NTag, {
  //       type: getStatusCodeType(row.status_code),
  //       size: 'small'
  //     }, {default: () => row.status_code})
  //   }
  // },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    align: 'center',
    render: (row, index) => {
      return h(NSpace, {size: 'small'}, [
        h(NButton, {
          size: 'tiny',
          type: 'primary',
          secondary: true,
          disabled: index === 0,
          onClick: () => moveCase(index, 'up')
        }, {default: () => '↑'}),
        h(NButton, {
          size: 'tiny',
          type: 'primary',
          secondary: true,
          disabled: index === planCases.value.length - 1,
          onClick: () => moveCase(index, 'down')
        }, {default: () => '↓'}),
        h(NButton, {
          size: 'small',
          type: 'success',
          onClick: () => handleExecuteTestCase(row)
        }, {default: () => '执行'}),
        // 如果有执行结果，显示查看结果按钮
        row.execution_result ? h(NButton, {
          size: 'small',
          type: 'info',
          secondary: true,
          onClick: () => handleViewCaseResult(row)
        }, {default: () => '结果'}) : null,
        h(NPopconfirm, {
          onPositiveClick: () => removeCaseFromPlan(row.id)
        }, {
          default: () => '确定移除吗？',
          trigger: () => h(NButton, {
            size: 'small',
            type: 'error',
            secondary: true
          }, {default: () => '移除'})
        })
      ])
    }
  }
]

// 模态框表格列配置
const modalTableColumns = [
  {
    title: '选择',
    key: 'select',
    width: 60,
    align: 'center',
    render: (row) => {
      return h(NCheckbox, {
        checked: selectedCaseIds.value.includes(row.id),
        onUpdateChecked: (checked) => handleCaseSelect(row.id, checked)
      })
    }
  },
  {
    title: '用例编号',
    key: 'case_number',
    width: 140,
    ellipsis: {tooltip: true}
  },
  {
    title: '用例名称',
    key: 'case_name',
    width: 200,
    ellipsis: {tooltip: true}
  },
  {
    title: '请求方式',
    key: 'method',
    width: 100,
    align: 'center',
    render: (row) => h(NTag, {
      size: 'small',
      type: row.method === 'GET' ? 'success' : row.method === 'POST' ? 'info' : 'warning'
    }, {default: () => row.method})
  },
  {
    title: 'URL',
    key: 'url',
    width: 300,
    ellipsis: {tooltip: true}
  },
  {
    title: '是否冒烟',
    key: 'is_smoke',
    width: 100,
    align: 'center',
    render: (row) => h(NTag, {
      type: row.is_smoke ? 'success' : 'default',
      size: 'small'
    }, {default: () => row.is_smoke ? '是' : '否'})
  }
]

// 处理测试用例选择
const handleCaseSelect = (caseId, checked) => {
  console.log('选择用例:', caseId, '选中状态:', checked)
  if (checked) {
    if (!selectedCaseIds.value.includes(caseId)) {
      selectedCaseIds.value.push(caseId)
    }
  } else {
    const index = selectedCaseIds.value.indexOf(caseId)
    if (index > -1) {
      selectedCaseIds.value.splice(index, 1)
    }
  }
  console.log('当前选中的用例ID:', selectedCaseIds.value)
}

// 打开添加用例模态框
const handleAddCases = async () => {
  console.log('=== 开始添加测试用例 ===')
  console.log('当前计划详情:', planDetail.value)

  // 确保计划详情已加载
  if (!planDetail.value || !planDetail.value.project_id) {
    console.error('项目信息未加载')
    $message.error('项目信息未加载，请刷新页面重试')
    return
  }

  try {
    // 重置数据
    availableCases.value = []
    filteredAvailableCases.value = []
    selectedCaseIds.value = []
    searchCaseName.value = ''

    console.log('正在获取可用测试用例...')
    await getAvailableCases()

    console.log('获取到的原始用例数据:', availableCases.value)

    // 过滤掉已经添加的用例
    const existingCaseIds = planCases.value.map(c => c.id)
    console.log('已存在的用例ID:', existingCaseIds)

    const filteredCases = availableCases.value.filter(c => !existingCaseIds.includes(c.id))
    console.log('过滤后的可用用例:', filteredCases)

    // 强制更新数据
    availableCases.value = [...filteredCases]
    filteredAvailableCases.value = [...filteredCases]

    // 打开模态框
    showAddCaseModal.value = true

    console.log('模态框已打开，最终可用用例数据:', availableCases.value)
  } catch (error) {
    console.error('获取可用测试用例失败:', error)
    $message.error('获取可用测试用例失败，请重试')
  }
}

// 添加用例到测试计划
const addCasesToPlan = async () => {
  if (selectedCaseIds.value.length === 0) {
    $message.warning('请选择要添加的测试用例')
    return
  }

  try {
    addCaseLoading.value = true
    console.error("-------------")
    console.error(addCaseLoading)
    await apiTestPlanApi.addTestCasesToPlan({
      plan_id: planId.value,
      case_ids: selectedCaseIds.value
    })
    $message.success('添加成功')
    showAddCaseModal.value = false
    selectedCaseIds.value = []
    await getPlanCases()
  } catch (error) {
    $message.error('添加失败')
    console.error(error)
  } finally {
    addCaseLoading.value = false
  }
}

// 移动用例顺序
const moveCase = async (currentIndex, direction) => {
  const cases = [...planCases.value]
  const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1

  if (targetIndex < 0 || targetIndex >= cases.length) {
    return
  }

  // 交换位置
  [cases[currentIndex], cases[targetIndex]] = [cases[targetIndex], cases[currentIndex]]

  // 更新执行顺序
  cases.forEach((item, index) => {
    item.execution_order = index + 1
  })

  try {
    // 更新本地数据
    planCases.value = cases

    // 调用后端接口更新顺序
    await apiTestPlanApi.updateCaseOrder({
      plan_id: planId.value,
      case_orders: cases.map(item => ({
        case_id: item.id,
        execution_order: item.execution_order
      }))
    })

    $message.success('顺序调整成功')
  } catch (error) {
    console.error('更新顺序失败:', error)
    $message.error('顺序调整失败')
    // 失败时重新获取数据
    await getPlanCases()
  }
}

// 从测试计划中移除用例
const removeCaseFromPlan = async (caseId) => {
  try {
    await apiTestPlanApi.removeTestCasesFromPlan({
      plan_id: planId.value,
      case_ids: [caseId]
    })
    $message.success('移除成功')
    await getPlanCases()
  } catch (error) {
    $message.error('移除失败')
    console.error(error)
  }
}

// 更新用例执行状态
const handleUpdateCaseExecutionStatus = async (caseId, status) => {
  try {
    await apiTestPlanApi.updateCaseExecutionStatus({
      plan_id: planId.value,
      case_id: caseId,
      execution_status: status
    })

    // 更新本地数据
    const caseIndex = planCases.value.findIndex(c => c.id === caseId)
    if (caseIndex !== -1) {
      planCases.value[caseIndex].execution_status = status
    }

    $message.success('执行状态更新成功')
  } catch (error) {
    console.error('更新执行状态失败:', error)
    $message.error('更新执行状态失败')
    // 失败时重新获取数据
    await getPlanCases()
  }
}

// 搜索用例
const handleSearchCases = () => {
  if (!searchCaseName.value.trim()) {
    filteredAvailableCases.value = [...availableCases.value]
  } else {
    filteredAvailableCases.value = availableCases.value.filter(testCase =>
      testCase.case_name.toLowerCase().includes(searchCaseName.value.toLowerCase())
    )
  }
}

// 清空搜索
const handleClearSearch = () => {
  searchCaseName.value = ''
  filteredAvailableCases.value = [...availableCases.value]
}

// 双击行事件处理
const handleRowDoubleClick = (row, index) => {
  console.log('双击编辑用例:', row, index)
  handleEditCase(row)
}

// 编辑用例
const handleEditCase = async (row) => {
  try {
    // 加载模块选项
    await loadModuleOptions(row.project_id)

    // 复制数据到编辑表单
    const editRow = {...row}

    // 处理params字段
    if (editRow.params) {
      if (typeof editRow.params === 'object') {
        editRow.params = JSON.stringify(editRow.params, null, 2)
      } else if (typeof editRow.params === 'string') {
        try {
          const parsed = JSON.parse(editRow.params)
          editRow.params = JSON.stringify(parsed, null, 2)
        } catch (e) {
          // 如果不是有效JSON，保持原样
        }
      }
    }

    // 处理断言数据
    if (editRow.expected_result) {
      try {
        editRow.assertions = JSON.parse(editRow.expected_result)
      } catch (e) {
        editRow.assertions = [{
          id: Date.now(),
          type: 'response_body',
          operator: 'contains',
          expected_value: editRow.expected_result,
          description: '从预期结果转换'
        }]
      }
    } else {
      editRow.assertions = []
    }

    // 处理变量提取数据
    if (editRow.variable_extracts) {
      try {
        editRow.variableExtracts = JSON.parse(editRow.variable_extracts)
      } catch (e) {
        editRow.variableExtracts = []
      }
    } else {
      editRow.variableExtracts = []
    }

    editCaseForm.value = editRow
    showEditCaseModal.value = true
  } catch (error) {
    console.error('编辑用例失败:', error)
    $message.error('编辑用例失败')
  }
}

// 加载模块选项
const loadModuleOptions = async (projectId) => {
  if (!projectId) {
    moduleOptions.value = []
    return
  }

  try {
    const response = await projectApi.getProjectModuleTree({project_id: projectId})
    // 将树形结构转换为平铺的选项列表
    const flattenModules = (modules, result = []) => {
      modules.forEach(module => {
        result.push({
          label: module.name,
          value: module.id
        })
        if (module.children && module.children.length > 0) {
          flattenModules(module.children, result)
        }
      })
      return result
    }

    moduleOptions.value = flattenModules(response.data || [])
  } catch (error) {
    console.error('加载模块选项失败:', error)
    moduleOptions.value = []
  }
}

// 保存编辑的用例
const handleSaveEditCase = async () => {
  try {
    editCaseLoading.value = true

    // 处理断言数据
    if (editCaseForm.value.assertions && editCaseForm.value.assertions.length > 0) {
      editCaseForm.value.expected_result = JSON.stringify(editCaseForm.value.assertions)
    } else {
      editCaseForm.value.expected_result = ''
    }

    // 处理变量提取数据
    if (editCaseForm.value.variableExtracts && editCaseForm.value.variableExtracts.length > 0) {
      editCaseForm.value.variable_extracts = JSON.stringify(editCaseForm.value.variableExtracts)
    } else {
      editCaseForm.value.variable_extracts = ''
    }

    await apiTestCaseApi.updateApiTestCase(editCaseForm.value)
    $message.success('用例更新成功')
    showEditCaseModal.value = false

    // 刷新用例列表
    await getPlanCases()
  } catch (error) {
    console.error('保存用例失败:', error)
    $message.error('保存用例失败')
  } finally {
    editCaseLoading.value = false
  }
}

// 断言管理
const assertionTypes = [
  {label: '状态码断言', value: 'status_code'},
  {label: '响应时间断言', value: 'response_time'},
  {label: '响应体JSON路径断言', value: 'json_path'},
  {label: '响应体内容断言', value: 'response_body'},
  {label: '响应头断言', value: 'response_header'}
]

// 变量提取管理
const variableExtractTypes = [
  {label: 'JSON路径提取', value: 'json_path'},
  {label: '响应头提取', value: 'header'},
  {label: '状态码提取', value: 'status_code'},
  {label: '正则表达式提取', value: 'regex'}
]

const assertionOperators = {
  status_code: [
    {label: '等于', value: 'equals'},
    {label: '不等于', value: 'not_equals'},
    {label: '大于', value: 'greater_than'},
    {label: '小于', value: 'less_than'},
    {label: '在范围内', value: 'in_range'}
  ],
  response_time: [
    {label: '小于', value: 'less_than'},
    {label: '大于', value: 'greater_than'},
    {label: '在范围内', value: 'in_range'}
  ],
  json_path: [
    {label: '等于', value: 'equals'},
    {label: '不等于', value: 'not_equals'},
    {label: '包含', value: 'contains'},
    {label: '不包含', value: 'not_contains'},
    {label: '正则匹配', value: 'regex'},
    {label: '存在', value: 'exists'},
    {label: '不存在', value: 'not_exists'}
  ],
  response_body: [
    {label: '等于', value: 'equals'},
    {label: '不等于', value: 'not_equals'},
    {label: '包含', value: 'contains'},
    {label: '不包含', value: 'not_contains'},
    {label: '正则匹配', value: 'regex'},
    {label: '精确匹配', value: 'exact_match'},
    {label: '部分匹配', value: 'partial_match'},
    {label: 'JSON Schema', value: 'json_schema'}
  ],
  response_header: [
    {label: '等于', value: 'equals'},
    {label: '不等于', value: 'not_equals'},
    {label: '包含', value: 'contains'},
    {label: '不包含', value: 'not_contains'},
    {label: '正则匹配', value: 'regex'},
    {label: '存在', value: 'exists'},
    {label: '不存在', value: 'not_exists'}
  ]
}

// 断言管理方法
const addAssertion = () => {
  if (!editCaseForm.value.assertions) {
    editCaseForm.value.assertions = []
  }
  editCaseForm.value.assertions.push({
    id: Date.now(),
    type: 'status_code',
    operator: 'equals',
    expected_value: '',
    json_path: '',
    header_name: '',
    description: ''
  })
}

const removeAssertion = (index) => {
  editCaseForm.value.assertions.splice(index, 1)
}

const getOperatorOptions = (type) => {
  return assertionOperators[type] || []
}

const getAssertionTypeLabel = (type) => {
  const typeOption = assertionTypes.find(t => t.value === type)
  return typeOption ? typeOption.label : type
}

const getAssertionOperatorLabel = (type, operator) => {
  const operators = assertionOperators[type] || []
  const operatorOption = operators.find(o => o.value === operator)
  return operatorOption ? operatorOption.label : operator
}

// 变量提取管理方法
const addVariableExtract = () => {
  if (!editCaseForm.value.variableExtracts) {
    editCaseForm.value.variableExtracts = []
  }
  editCaseForm.value.variableExtracts.push({
    id: Date.now(),
    variable_name: '',
    extract_type: 'json_path',
    extract_path: '',
    default_value: '',
    description: ''
  })
}

const removeVariableExtract = (index) => {
  editCaseForm.value.variableExtracts.splice(index, 1)
}

const getVariableExtractTypeLabel = (type) => {
  const typeOption = variableExtractTypes.find(t => t.value === type)
  return typeOption ? typeOption.label : type
}

// 执行测试用例
const handleExecuteTestCase = async (row) => {
  // if (!selectedEnvironment.value) {
  //   $message.error('请先选择执行环境')
  //   return
  // }

  try {
    executionLoading.value = true
    executionModalVisible.value = true
    executionResult.value = null

    const response = await apiTestCaseApi.executeApiTestCase({
      test_case_id: row.id,
      environment_id: planDetail.value.environment_id
    })

    if (response.code === 200) {
      executionResult.value = response.data
      // 刷新表格以更新执行次数和最后执行时间
      await getPlanCases()
    } else {
      $message.error(response.msg || '测试用例执行失败')
    }
  } catch (error) {
    console.error('执行测试用例失败:', error)
    $message.error('执行测试用例失败: ' + error.message)
  } finally {
    executionLoading.value = false
  }
}

// 关闭执行结果弹窗
const handleCloseExecutionModal = () => {
  executionModalVisible.value = false
  executionResult.value = null
}

// 查看用例执行结果
const handleViewCaseResult = (row) => {
  console.log('查看用例执行结果:', row)

  // 构造执行结果数据
  const result = {
    success: row.execution_result === 'success',
    status_code: row.status_code,
    execution_time: row.response_time,
    error_message: row.error_message,
    response_body: row.response_body,
    response_headers: row.response_headers,
    assertion_results: row.assertion_results ? {
      results: row.assertion_results,
      all_passed: row.assertion_results.every(a => a.passed),
      passed_count: row.assertion_results.filter(a => a.passed).length,
      total_count: row.assertion_results.length
    } : null,
    request_info: {
      method: row.method,
      url: row.url,
      params: row.params,
      body: row.body
    }
  }

  // 如果没有错误信息但有断言结果，生成断言结果描述
  if (!result.error_message && result.assertion_results) {
    result.assertion_summary = formatAssertionResult(row.assertion_results)
  }

  executionResult.value = result
  executionModalVisible.value = true
}

// 获取状态码类型
const getStatusCodeType = (statusCode) => {
  if (!statusCode) return 'default'
  if (statusCode >= 200 && statusCode < 300) return 'success'
  if (statusCode >= 300 && statusCode < 400) return 'info'
  if (statusCode >= 400 && statusCode < 500) return 'warning'
  if (statusCode >= 500) return 'error'
  return 'default'
}

// 复制响应体内容
const handleCopyResponse = async (content) => {
  try {
    const textToCopy = typeof content === 'object' ? JSON.stringify(content, null, 2) : content
    await navigator.clipboard.writeText(textToCopy)
    $message.success('复制成功')
  } catch (err) {
    $message.error('复制失败')
    console.error('复制失败:', err)
  }
}

// 解析URL和查询参数
const parseUrlAndParams = (url, method) => {
  if (method !== 'GET' || !url) {
    return {cleanUrl: url, params: ''}
  }

  try {
    const urlObj = new URL(url.startsWith('http') ? url : `http://example.com${url}`)
    const cleanUrl = urlObj.pathname
    const searchParams = urlObj.searchParams

    if (searchParams.toString()) {
      const paramsObj = {}
      for (const [key, value] of searchParams) {
        // 尝试转换数字
        if (!isNaN(value) && value !== '') {
          paramsObj[key] = Number(value)
        } else {
          paramsObj[key] = value
        }
      }
      return {
        cleanUrl,
        params: JSON.stringify(paramsObj, null, 2)
      }
    }

    return {cleanUrl, params: ''}
  } catch (error) {
    console.error('URL解析失败:', error)
    return {cleanUrl: url, params: ''}
  }
}

// 监听URL和方法变化，自动解析参数
const handleUrlOrMethodChange = () => {
  if (editCaseForm.value.method === 'GET' && editCaseForm.value.url) {
    const {cleanUrl, params} = parseUrlAndParams(editCaseForm.value.url, editCaseForm.value.method)
    editCaseForm.value.url = cleanUrl
    if (params && !editCaseForm.value.params) {
      editCaseForm.value.params = params
    }
  }
}

// 格式化JSON
const formatJson = (jsonString) => {
  try {
    if (typeof jsonString === 'object') {
      return JSON.stringify(jsonString, null, 2)
    }
    if (typeof jsonString === 'string') {
      // 尝试解析为JSON
      const parsed = JSON.parse(jsonString)
      return JSON.stringify(parsed, null, 2)
    }
    return String(jsonString || '')
  } catch (e) {
    // 如果不是有效的JSON，直接返回原始字符串
    return String(jsonString || '')
  }
}

// 格式化响应体
const formatResponseBody = (body) => {
  if (!body) return ''
  try {
    if (typeof body === 'object') {
      return JSON.stringify(body, null, 2)
    }
    if (typeof body === 'string') {
      // 尝试解析为JSON
      const parsed = JSON.parse(body)
      return JSON.stringify(parsed, null, 2)
    }
    return String(body)
  } catch (e) {
    // 如果不是有效的JSON，直接返回原始字符串
    return String(body)
  }
}

// 格式化时间到秒
const formatDateTime = (dateTimeString) => {
  if (!dateTimeString) return '-'
  try {
    const date = new Date(dateTimeString)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  } catch (e) {
    return dateTimeString
  }
}

// 格式化断言结果显示
const formatAssertionResult = (assertionResults) => {
  if (!assertionResults || !Array.isArray(assertionResults)) {
    return '无断言'
  }

  const totalCount = assertionResults.length
  const passedCount = assertionResults.filter(a => a.passed).length
  const allPassed = passedCount === totalCount

  return `断言结果: ${allPassed ? '全部通过' : '部分失败'} (${passedCount}/${totalCount})`
}

// 获取环境列表
// const getEnvironmentList = async () => {
//   try {
//     const {data} = await projectApi.getEnvironmentList({project_id: planDetail.value.project_id})
//     environmentOptions.value = data.map(env => ({
//       label: env.environment_name,
//       value: env.id
//     }))
//   } catch (error) {
//     console.error('获取环境列表失败:', error)
//     environmentOptions.value = []
//   }
// }

// 批量执行测试用例
const handleBatchExecute = async () => {
  if (!planDetail.value.environment_id) {
    $message.error('请先在基本信息中配置运行环境')
    return
  }

  if (planCases.value.length === 0) {
    $message.error('当前测试计划没有关联的测试用例')
    return
  }

  try {
    batchExecutionLoading.value = true
    $message.info('开始批量执行测试用例，请稍候...')

    const response = await apiTestPlanApi.batchExecuteApiTestPlan({
      plan_id: planId.value,
      environment_id: planDetail.value.environment_id
    })

    if (response.code === 200) {
      const result = response.data
      $message.success(`批量执行完成！通过率: ${result.pass_rate}%`)

      // 刷新计划详情和用例列表
      await getPlanDetail()
      await getPlanCases()

      // 显示测试报告，添加报告时间
      result.report_time = formatDateTime(new Date().toISOString())
      testReportData.value = result
      testReportVisible.value = true

      console.log('批量执行结果:', result)
    } else {
      $message.error(response.msg || '批量执行失败')
    }
  } catch (error) {
    console.error('批量执行失败:', error)
    $message.error('批量执行失败: ' + error.message)
  } finally {
    batchExecutionLoading.value = false
  }
}

// 查看测试报告
const handleViewTestReport = async () => {
  try {
    // 获取最新的测试计划详情和用例结果
    await getPlanDetail()
    await getPlanCases()

    // 构建测试报告数据
    const reportData = {
      total_cases: planCases.value.length,
      passed_cases: planCases.value.filter(c => c.execution_result === 'success').length,
      failed_cases: planCases.value.filter(c => c.execution_result === 'failed').length,
      pass_rate: planDetail.value.pass_rate || 0,
      execution_result: planDetail.value.execution_result,
      execution_time: planCases.value.reduce((total, c) => total + (c.response_time || 0), 0),
      report_time: planDetail.value.last_execution_time ? formatDateTime(planDetail.value.last_execution_time) : formatDateTime(new Date().toISOString()),
      case_results: planCases.value.map(c => ({
        case_id: c.id,
        case_number: c.case_number,
        case_name: c.case_name,
        success: c.execution_result === 'success',
        status_code: c.status_code,
        response_time: c.response_time || 0,
        error_message: c.error_message,
        response_headers: c.response_headers,
        response_body: c.response_body,
        assertion_results: c.assertion_results || []
      }))
    }

    testReportData.value = reportData
    testReportVisible.value = true
  } catch (error) {
    console.error('获取测试报告失败:', error)
    $message.error('获取测试报告失败')
  }
}

// 返回列表
const goBack = () => {
  router.push('/test_plan/api_test_plan')
}

// 生成Cron表达式
const generateCronExpression = () => {
  switch (cronConfig.value.type) {
    case 'minutely':
      // 每N分钟执行一次
      const minuteInterval = cronConfig.value.interval || 1
      if (minuteInterval === 1) {
        return '* * * * *' // 每分钟执行
      } else {
        return `*/${minuteInterval} * * * *` // 每N分钟执行
      }
    case 'hourly':
      // 每N小时执行一次
      const hourInterval = cronConfig.value.interval || 1
      if (hourInterval === 1) {
        return '0 * * * *' // 每小时执行
      } else {
        return `0 */${hourInterval} * * *` // 每N小时执行
      }
    case 'daily':
      if (!cronConfig.value.time) {
        return '0 9 * * *' // 默认每天9点
      }
      const [hour, minute] = cronConfig.value.time.split(':').map(Number)
      return `${minute} ${hour} * * *`
    case 'weekly':
      if (!cronConfig.value.time) {
        return '0 9 * * 1-5' // 默认工作日9点
      }
      const [weeklyHour, weeklyMinute] = cronConfig.value.time.split(':').map(Number)
      const weekdays = cronConfig.value.weekdays.sort().join(',')
      return `${weeklyMinute} ${weeklyHour} * * ${weekdays}`
    case 'monthly':
      if (!cronConfig.value.time) {
        return '0 9 1 * *' // 默认每月1号9点
      }
      const [monthlyHour, monthlyMinute] = cronConfig.value.time.split(':').map(Number)
      return `${monthlyMinute} ${monthlyHour} ${cronConfig.value.dayOfMonth} * *`
    case 'custom':
      return scheduledTaskForm.value.cron_expression
    default:
      return '0 9 * * 1-5'
  }
}

// 更新Cron表达式
const updateCronExpression = () => {
  if (cronConfig.value.type !== 'custom') {
    scheduledTaskForm.value.cron_expression = generateCronExpression()
  }
}

// 获取Cron表达式描述
const getCronDescription = () => {
  const time = cronConfig.value.time
  const interval = cronConfig.value.interval || 1
  switch (cronConfig.value.type) {
    case 'minutely':
      return interval === 1 ? '每分钟执行一次' : `每${interval}分钟执行一次`
    case 'hourly':
      return interval === 1 ? '每小时执行一次' : `每${interval}小时执行一次`
    case 'daily':
      return `每天 ${time || '09:00'} 执行`
    case 'weekly':
      const weekdayNames = cronConfig.value.weekdays.map(day => {
        const option = weekdayOptions.find(opt => opt.value === day)
        return option ? option.label : ''
      }).join('、')
      return `每周 ${weekdayNames} ${time || '09:00'} 执行`
    case 'monthly':
      return `每月 ${cronConfig.value.dayOfMonth} 日 ${time || '09:00'} 执行`
    case 'custom':
      return '自定义表达式'
    default:
      return ''
  }
}

// 定时任务相关方法
const handleScheduledTask = () => {
  if (existingScheduledTask.value) {
    // 如果已有定时任务，显示编辑模式
    scheduledTaskForm.value = {
      id: existingScheduledTask.value.id,
      task_name: existingScheduledTask.value.task_name,
      cron_expression: existingScheduledTask.value.cron_expression,
      description: existingScheduledTask.value.description || ''
    }
    // 解析现有的cron表达式到配置
    parseCronExpression(existingScheduledTask.value.cron_expression)
  } else {
    // 新建定时任务
    cronConfig.value = {
      type: 'daily',
      time: '09:00', // 使用字符串格式
      weekdays: [1, 2, 3, 4, 5], // 默认工作日
      dayOfMonth: 1,
      interval: 1
    }

    scheduledTaskForm.value = {
      task_name: `${planDetail.value.plan_name || '测试计划'}_定时任务`,
      cron_expression: generateCronExpression(),
      description: `定时执行测试计划：${planDetail.value.plan_name || ''}`
    }
  }
  scheduledTaskVisible.value = true
}

const handleSaveScheduledTask = async () => {
  try {
    scheduledTaskLoading.value = true

    // 验证表单
    if (!scheduledTaskForm.value.task_name) {
      $message.error('请输入任务名称')
      return
    }
    if (!scheduledTaskForm.value.cron_expression) {
      $message.error('请输入Cron表达式')
      return
    }

    // 验证Cron表达式
    const validateResult = await scheduledTaskApi.validateCronExpression({
      cron_expression: scheduledTaskForm.value.cron_expression
    })

    if (validateResult.code !== 200) {
      $message.error(validateResult.msg || 'Cron表达式无效')
      return
    }

    let result
    if (scheduledTaskForm.value.id) {
      // 更新现有定时任务
      result = await scheduledTaskApi.updateScheduledTask({
        id: scheduledTaskForm.value.id,
        task_name: scheduledTaskForm.value.task_name,
        cron_expression: scheduledTaskForm.value.cron_expression,
        description: scheduledTaskForm.value.description
      })
    } else {
      // 创建新定时任务
      result = await scheduledTaskApi.createScheduledTask({
        task_name: scheduledTaskForm.value.task_name,
        plan_id: planId.value,
        cron_expression: scheduledTaskForm.value.cron_expression,
        description: scheduledTaskForm.value.description
      })
    }

    if (result.code === 200) {
      $message.success(scheduledTaskForm.value.id ? '定时任务更新成功' : '定时任务创建成功')
      scheduledTaskVisible.value = false
      // 重新获取定时任务信息
      await getScheduledTask()
    } else {
      $message.error(result.msg || (scheduledTaskForm.value.id ? '更新定时任务失败' : '创建定时任务失败'))
    }
  } catch (error) {
    console.error('保存定时任务失败:', error)
    $message.error('保存定时任务失败: ' + error.message)
  } finally {
    scheduledTaskLoading.value = false
  }
}

// 解析cron表达式到配置
const parseCronExpression = (cronExpr) => {
  // 简单的cron表达式解析，这里只处理常见格式
  // 实际项目中可能需要更复杂的解析逻辑
  const parts = cronExpr.split(' ')
  if (parts.length === 5) {
    const [minute, hour, dayOfMonth, month, dayOfWeek] = parts

    // 检查是否为每分钟执行
    if (minute === '*' && hour === '*' && dayOfMonth === '*' && month === '*' && dayOfWeek === '*') {
      cronConfig.value.type = 'minutely'
      cronConfig.value.interval = 1
      return
    }

    // 检查是否为每N分钟执行
    if (minute.startsWith('*/') && hour === '*' && dayOfMonth === '*' && month === '*' && dayOfWeek === '*') {
      cronConfig.value.type = 'minutely'
      cronConfig.value.interval = parseInt(minute.substring(2))
      return
    }

    // 检查是否为每小时执行
    if (minute === '0' && hour === '*' && dayOfMonth === '*' && month === '*' && dayOfWeek === '*') {
      cronConfig.value.type = 'hourly'
      cronConfig.value.interval = 1
      return
    }

    // 检查是否为每N小时执行
    if (minute === '0' && hour.startsWith('*/') && dayOfMonth === '*' && month === '*' && dayOfWeek === '*') {
      cronConfig.value.type = 'hourly'
      cronConfig.value.interval = parseInt(hour.substring(2))
      return
    }

    if (dayOfWeek !== '*' && dayOfMonth === '*') {
      // 每周执行
      cronConfig.value.type = 'weekly'
      cronConfig.value.time = `${hour.padStart(2, '0')}:${minute.padStart(2, '0')}`
      cronConfig.value.weekdays = dayOfWeek.split(',').map(d => parseInt(d) === 0 ? 7 : parseInt(d))
    } else if (dayOfMonth !== '*' && dayOfWeek === '*') {
      // 每月执行
      cronConfig.value.type = 'monthly'
      cronConfig.value.time = `${hour.padStart(2, '0')}:${minute.padStart(2, '0')}`
      cronConfig.value.dayOfMonth = parseInt(dayOfMonth)
    } else if (dayOfMonth === '*' && dayOfWeek === '*' && hour !== '*') {
      // 每天执行
      cronConfig.value.type = 'daily'
      cronConfig.value.time = `${hour.padStart(2, '0')}:${minute.padStart(2, '0')}`
    } else {
      // 自定义表达式
      cronConfig.value.type = 'custom'
    }
  } else {
    cronConfig.value.type = 'custom'
  }
}

// 删除定时任务
const handleDeleteScheduledTask = async () => {
  if (!existingScheduledTask.value) return

  try {
    const result = await scheduledTaskApi.deleteScheduledTask({
      task_id: existingScheduledTask.value.id
    })

    if (result.code === 200) {
      $message.success('定时任务删除成功')
      existingScheduledTask.value = null
    } else {
      $message.error(result.msg || '删除定时任务失败')
    }
  } catch (error) {
    console.error('删除定时任务失败:', error)
    $message.error('删除定时任务失败: ' + error.message)
  }
}

// 处理钉钉开关变化
const handleDingtalkToggle = async (value) => {
  try {
    await apiTestPlanApi.updateApiTestPlan({
      id: planId.value,
      plan_name: planDetail.value.plan_name,
      level: planDetail.value.level,
      status: planDetail.value.status,
      description: planDetail.value.description,
      project_id: planDetail.value.project_id,
      environment_id: planDetail.value.environment_id,
      enable_dingtalk: value
    })

    // 更新本地状态
    planDetail.value.enable_dingtalk = value
    $message.success(value ? '已启用钉钉通知' : '已关闭钉钉通知')
  } catch (error) {
    console.error('更新钉钉设置失败:', error)
    $message.error('更新钉钉设置失败')
    // 恢复开关状态
    enableDingtalk.value = !value
  }
}

// 组件挂载
onMounted(async () => {
  await getPlanDetail()
  await getPlanCases()
  await getScheduledTask() // 获取定时任务信息
  if (planDetail.value.project_id) {
    // await getEnvironmentList()
  }
})
</script>

<template>
  <CommonPage show-footer>
    <template #action>
      <NSpace>
        <!--        <NSelect-->
        <!--          v-model:value="selectedEnvironment"-->
        <!--          :options="environmentOptions"-->
        <!--          placeholder="选择执行环境"-->
        <!--          style="width: 200px;"-->
        <!--          clearable-->
        <!--        />-->
        <NButton @click="goBack">
          <TheIcon icon="material-symbols:arrow-back" :size="18" class="mr-5"/>
          返回
        </NButton>
        <NButton type="primary" @click="handleAddCases">
          <TheIcon icon="material-symbols:add" :size="18" class="mr-5"/>
          添加测试用例
        </NButton>
      </NSpace>
    </template>

    <!-- 左右布局 -->
    <NGrid :cols="24" :x-gap="16">
      <!-- 左侧：基本信息 -->
      <NGridItem :span="5">
        <NCard title="基本信息" class="detail-card">
          <div class="detail-info">
            <div class="detail-item">
              <span class="detail-label">计划名称:</span>
              <span class="detail-value">{{ planDetail.plan_name || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">等级:</span>
              <NTag v-if="planDetail.level" :type="levelMap[planDetail.level]?.type" size="small">
                {{ levelMap[planDetail.level]?.text }}
              </NTag>
              <span v-else class="detail-value">-</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">状态:</span>
              <NTag v-if="planDetail.status" :type="statusMap[planDetail.status]?.type" size="small">
                {{ statusMap[planDetail.status]?.text }}
              </NTag>
              <span v-else class="detail-value">-</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">运行环境:</span>
              <span class="detail-value">{{ planDetail.environment_name || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">执行结果:</span>
              <NTag v-if="planDetail.execution_result"
                    :type="planDetail.execution_result === 'success' ? 'success' : 'error'"
                    size="small">
                {{ planDetail.execution_result === 'success' ? '成功' : '失败' }}
              </NTag>
              <span v-else class="detail-value">-</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">通过率:</span>
              <span class="detail-value">{{ planDetail.pass_rate !== null ? `${planDetail.pass_rate}%` : '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">创建时间:</span>
              <span class="detail-value">{{ planDetail.created_at ? formatDate(planDetail.created_at) : '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">最近执行:</span>
              <span class="detail-value">{{
                  planDetail.last_execution_time ? formatDateTime(planDetail.last_execution_time) : '-'
                }}</span>
            </div>
            <!-- 定时任务信息 -->
            <div v-if="existingScheduledTask" class="detail-item">
              <span class="detail-label">定时任务:</span>
              <div class="detail-value">
                <div style="margin-bottom: 4px;">
                  <NTag type="success" size="small">{{ existingScheduledTask.is_active ? '已启用' : '已禁用' }}</NTag>
                </div>

              </div>
            </div>
            <div v-if="existingScheduledTask" class="detail-item">
              <span class="detail-label">下次执行:</span>
              <div class="detail-value">
                <div>
                   {{ existingScheduledTask.next_run_time ? formatDateTime(existingScheduledTask.next_run_time) : '-' }}
                </div>
              </div>
            </div>
            <div class="detail-item">
              <span class="detail-label">创建人:</span>
              <span class="detail-value">{{ planDetail.creator_name || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">描述:</span>
              <div class="detail-description">
                {{ planDetail.description || '-' }}
              </div>
            </div>
          </div>


        </NCard>
      </NGridItem>

      <!-- 右侧：关联的测试用例 -->
      <NGridItem :span="19">
        <NCard title="已添加用例" class="detail-card">

          <template #header-extra>
            <NSpace>
              <div style="display: flex; align-items: center; margin-right: 8px;">
                <NSwitch
                  v-model:value="enableDingtalk"
                  @update:value="handleDingtalkToggle"
                  size="large"
                >
                  <template #checked>通知</template>
                  <template #unchecked>停止</template>
                </NSwitch>
              </div>
              <NButton
                  type="warning"
                  size="small"
                  @click="handleScheduledTask"
                  :disabled="planCases.length === 0"
              >
                <TheIcon icon="material-symbols:schedule" :size="small" class="mr-5"/>
                {{ existingScheduledTask ? '编辑定时' : '创建定时' }}
              </NButton>
              <NButton
                  v-if="existingScheduledTask"
                  type="error"
                  size="small"
                  @click="handleDeleteScheduledTask"
                  secondary
              >
                <TheIcon icon="material-symbols:delete" :size="small" class="mr-5"/>
                删除定时
              </NButton>
              <NButton
                  type="primary"
                  size="small"
                  :loading="batchExecutionLoading"
                  @click="handleBatchExecute"
                  :disabled="planCases.length === 0"
              >
                <TheIcon icon="material-symbols:play-arrow" :size="small" class="mr-5"/>
                执行
              </NButton>
              <NButton
                  type="info"
                  size="small"
                  @click="handleViewTestReport"
                  :disabled="!planDetail.execution_result || planDetail.execution_result === 'running'"
              >
                测试报告
              </NButton>
<!--              <span class="case-count">共 {{ planCases.length }} 个用例</span>-->
            </NSpace>
          </template>
          <n-data-table
              :columns="caseColumns"
              :data="planCases"
              :loading="loading"
              :pagination="false"
              :scroll-x="1000"
              size="small"
              :max-height="600"
              @row-dblclick="handleRowDoubleClick"
          />
        </NCard>
      </NGridItem>
    </NGrid>
  </CommonPage>

  <!-- 添加测试用例模态框 -->
  <NModal
      v-model:show="showAddCaseModal"
      preset="dialog"
      title="添加测试用例"
      :style="{ width: '1000px' }"
      positive-text="确定"
      negative-text="取消"
      :positive-button-props="{ loading: addCaseLoading }"
      @positive-click="addCasesToPlan"
  >
    <!-- 搜索栏 -->
    <div style="margin-bottom: 16px;">
      <NSpace>
        <NInput
            v-model:value="searchCaseName"
            placeholder="请输入用例名称搜索"
            clearable
            style="width: 300px;"
            @keypress.enter="handleSearchCases"
            @clear="handleClearSearch"
        />
        <NButton type="primary" @click="handleSearchCases">搜索</NButton>
        <NButton @click="handleClearSearch">清空</NButton>
      </NSpace>
    </div>

    <!-- 用例列表 -->
    <div v-if="!availableCases || availableCases.length === 0" class="empty-state">
      <p>当前项目没有已审核的测试用例可以添加</p>
      <p class="text-gray">请确保项目中有状态为"已审核"的测试用例</p>
    </div>
    <div v-else class="modal-content">
      <n-data-table
          :columns="modalTableColumns"
          :data="filteredAvailableCases"
          :pagination="false"
          size="small"
          :scroll-x="800"
          :max-height="400"
          striped
          :row-key="(row) => row.id"
      />
    </div>

    <div class="mt-3">
      <span>已选择 {{ selectedCaseIds.length }} 个测试用例</span>
      <span v-if="filteredAvailableCases && filteredAvailableCases.length > 0" class="ml-4 text-gray">
        （当前显示 {{ filteredAvailableCases.length }} 个用例，共 {{ availableCases.length }} 个可选用例）
      </span>
    </div>
  </NModal>

  <!-- 编辑测试用例模态框 -->
  <CrudModal
      v-model:visible="showEditCaseModal"
      title="编辑测试用例"
      :loading="editCaseLoading"
      @save="handleSaveEditCase"
      width="800px"
  >
    <NForm
        label-placement="left"
        label-align="left"
        :label-width="100"
        :model="editCaseForm"
    >
      <NFormItem label="用例名称" path="case_name">
        <NInput v-model:value="editCaseForm.case_name" clearable placeholder="请输入用例名称" :disabled="!active"/>
      </NFormItem>
      <NFormItem label="请求方式" path="method">
        <NSelect
            v-model:value="editCaseForm.method"
            :options="methodOptions"
            placeholder="请选择请求方式"
            @update:value="handleUrlOrMethodChange"
            :disabled="!active"
        />
      </NFormItem>
      <NFormItem label="请求URL" path="url">
        <NInput
            v-model:value="editCaseForm.url"
            clearable
            placeholder="请输入请求URL"
            @blur="handleUrlOrMethodChange"
            :disabled="!active"
        />
      </NFormItem>
      <!--      <NFormItem label="所属模块" path="module_id">-->
      <!--        <NSelect-->
      <!--            v-model:value="editCaseForm.module_id"-->
      <!--            :options="moduleOptions"-->
      <!--            placeholder="请选择所属模块"-->
      <!--            clearable-->
      <!--        />-->
      <!--      </NFormItem>-->
      <NFormItem label="请求参数" path="params">
        <NInput
            v-model:value="editCaseForm.params"
            type="textarea"
            placeholder="请输入请求参数（JSON格式）"
            :rows="4"
        />
      </NFormItem>
      <NFormItem label="请求体" path="body">
        <NInput
            v-model:value="editCaseForm.body"
            type="textarea"
            placeholder="请输入请求体"
            :rows="4"
        />
      </NFormItem>

      <NFormItem label="断言配置" path="assertions" style="width: 100%;">
        <div style="border: 1px solid #e0e0e0; border-radius: 4px; padding: 8px; width: 100%;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
            <span style="font-weight: 500;">断言规则</span>
            <NButton size="small" type="primary" @click="addAssertion">
              <TheIcon icon="material-symbols:add" :size="16" class="mr-5"/>
              添加断言
            </NButton>
          </div>
          <div v-for="(assertion, index) in editCaseForm.assertions" :key="assertion.id"
               style="border: 1px solid #f0f0f0; border-radius: 4px; padding: 12px; margin-bottom: 12px; background: #fafafa;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
              <span style="font-weight: 500; color: #666;">断言 {{ index + 1 }}</span>
              <NButton size="tiny" type="error" @click="removeAssertion(index)">
                <TheIcon icon="material-symbols:delete" :size="14"/>
              </NButton>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 12px; margin-bottom: 4px;">
              <NFormItem label="断言类型" :show-label="false">
                <NSelect
                    v-model:value="assertion.type"
                    :options="assertionTypes"
                    placeholder="选择断言类型"
                    size="small"
                />
              </NFormItem>
              <NFormItem label="操作符" :show-label="false">
                <NSelect
                    v-model:value="assertion.operator"
                    :options="getOperatorOptions(assertion.type)"
                    placeholder="选择操作符"
                    size="small"
                />
              </NFormItem>
              <NFormItem v-if="!['exists', 'not_exists', 'json_schema'].includes(assertion.operator)" label="期望值"
                         :show-label="false">
                <NInput
                    v-model:value="assertion.expected_value"
                    placeholder="请输入期望值"
                    size="small"
                />
              </NFormItem>
            </div>

            <div v-if="assertion.type === 'json_path'" style="margin-bottom: 2px;">
              <NFormItem label="JSON路径" :show-label="false">
                <NInput
                    v-model:value="assertion.json_path"
                    placeholder="例如: $.data.id 或 $.users[0].name"
                    size="small"
                />
              </NFormItem>
            </div>

            <div v-if="assertion.type === 'response_header'" style="margin-bottom: 2px;">
              <NFormItem label="响应头名称" :show-label="false">
                <NInput
                    v-model:value="assertion.header_name"
                    placeholder="例如: Content-Type"
                    size="small"
                />
              </NFormItem>
            </div>

            <div v-if="assertion.operator === 'json_schema'" style="margin-bottom: 2px;">
              <NFormItem label="JSON Schema" :show-label="false">
                <NInput
                    v-model:value="assertion.expected_value"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入JSON Schema"
                    size="small"
                />
              </NFormItem>
            </div>
            <NFormItem label="描述" :show-label="false" style="margin-bottom: 0; margin-top: -4px;">
              <NInput
                  v-model:value="assertion.description"
                  placeholder="断言描述（可选）"
                  size="small"
              />
            </NFormItem>
          </div>
        </div>
      </NFormItem>

      <!-- 变量提取配置 -->
      <NFormItem label="变量提取配置" path="variableExtracts" style="width: 100%;">
        <div style="border: 1px solid #e0e0e0; border-radius: 4px; padding: 8px; width: 100%;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
            <span style="font-weight: 500;">变量提取规则</span>
            <NButton size="small" type="success" @click="addVariableExtract">
              <TheIcon icon="material-symbols:add" :size="16" class="mr-5"/>
              添加变量提取
            </NButton>
          </div>
          <div v-if="!editCaseForm.variableExtracts || editCaseForm.variableExtracts.length === 0"
               style="text-align: center; color: #999; padding: 20px;">
            暂无变量提取配置，点击上方按钮添加
          </div>
          <div v-for="(extract, index) in editCaseForm.variableExtracts" :key="extract.id"
               style="border: 1px solid #f0f0f0; border-radius: 4px; padding: 12px; margin-bottom: 12px; background: #fafafa;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
              <span style="font-weight: 500; color: #666;">变量提取 {{ index + 1 }}</span>
              <NButton size="tiny" type="error" @click="removeVariableExtract(index)">
                <TheIcon icon="material-symbols:delete" :size="14"/>
              </NButton>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 8px;">
              <NFormItem label="变量名称" :show-label="false">
                <NInput
                    v-model:value="extract.variable_name"
                    placeholder="变量名称，如: user_id"
                    size="small"
                />
              </NFormItem>
              <NFormItem label="提取类型" :show-label="false">
                <NSelect
                    v-model:value="extract.extract_type"
                    :options="variableExtractTypes"
                    placeholder="选择提取类型"
                    size="small"
                />
              </NFormItem>
            </div>

            <div style="margin-bottom: 8px;">
              <NFormItem label="提取路径" :show-label="false">
                <NInput
                    v-model:value="extract.extract_path"
                    :placeholder="extract.extract_type === 'json_path' ? '例如: $.data.id 或 $.users[0].name' :
                                 extract.extract_type === 'header' ? '例如: Content-Type' :
                                 extract.extract_type === 'regex' ? '例如: user_id=(\\d+)' : '提取路径'"
                    size="small"
                />
              </NFormItem>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 2fr; gap: 12px; margin-bottom: 4px;">
              <NFormItem label="默认值" :show-label="false">
                <NInput
                    v-model:value="extract.default_value"
                    placeholder="默认值（可选）"
                    size="small"
                />
              </NFormItem>
              <NFormItem label="描述" :show-label="false">
                <NInput
                    v-model:value="extract.description"
                    placeholder="变量描述（可选）"
                    size="small"
                />
              </NFormItem>
            </div>
          </div>
        </div>
      </NFormItem>

      <!--      <NFormItem label="是否冒烟用例" path="is_smoke">-->
      <!--        <NSwitch v-model:value="editCaseForm.is_smoke"/>-->
      <!--      </NFormItem>-->
      <!--      <NFormItem label="状态" path="status">-->
      <!--        <NSelect-->
      <!--            v-model:value="editCaseForm.status"-->
      <!--            :options="statusOptions"-->
      <!--            placeholder="请选择状态"-->
      <!--        />-->
      <!--      </NFormItem>-->
    </NForm>
  </CrudModal>

  <!-- 执行结果弹窗 -->
  <NModal
      v-model:show="executionModalVisible"
      preset="card"
      title="测试用例执行结果"
      style="width: 90%; max-width: 800px;"
      :mask-closable="true"
      :closable="true"
      :auto-focus="false"
  >
    <div style="height: 70vh; overflow: hidden; display: flex; flex-direction: column;">
      <NSpin :show="executionLoading" style="flex: 1;">
        <div v-if="executionResult" style="height: 100%; display: flex; flex-direction: column;">
          <!-- 基本信息 -->
          <div style="margin-bottom: 16px; flex-shrink: 0;">
            <NTag
                :type="executionResult.success ? 'success' : 'error'"
                style="margin-right: 8px;"
                size="medium"
            >
              {{ executionResult.success ? '✓ 执行成功' : '✗ 执行失败' }}
            </NTag>
            <NTag
                :type="getStatusCodeType(executionResult.status_code)"
                style="margin-right: 8px;"
                size="medium"
            >
              状态码: {{ executionResult.status_code || '无' }}
            </NTag>
            <NTag type="info" size="medium">
              执行时间: {{ executionResult.execution_time }}ms
            </NTag>
            <NTag
                v-if="executionResult.assertion_results"
                :type="executionResult.assertion_results.all_passed ? 'success' : 'error'"
                style="margin-left: 8px;"
                size="medium"
            >
              断言结果: {{ executionResult.assertion_results.all_passed ? '全部通过' : '存在失败' }}
              ({{ executionResult.assertion_results.passed_count }}/{{ executionResult.assertion_results.total_count }})
            </NTag>
          </div>

          <!-- 断言结果信息 -->
<!--          <div v-if="executionResult.assertion_summary"-->
<!--               style="margin-bottom: 16px; flex-shrink: 0;">-->
<!--            <NCard title="断言结果" size="small" :style="`border: 1px solid ${executionResult.success ? '#52c41a' : '#f56565'};`">-->
<!--              <div :style="`max-height: 150px; overflow-y: auto; background: ${executionResult.success ? '#f6ffed' : '#fef5e7'}; padding: 12px; border-radius: 4px;`">-->
<!--                <pre-->
<!--                    style="margin: 0; white-space: pre-wrap; word-break: break-word; font-family: 'Courier New', monospace; font-size: 13px;">{{-->
<!--                    executionResult.assertion_summary-->
<!--                  }}</pre>-->
<!--              </div>-->
<!--            </NCard>-->
<!--          </div>-->

          <!-- 错误信息（仅在没有断言结果时显示） -->
<!--          <div v-if="!executionResult.success && executionResult.error_message && !executionResult.assertion_summary"-->
<!--               style="margin-bottom: 16px; flex-shrink: 0;">-->
<!--            <NCard title="错误信息" size="small" style="border: 1px solid #f56565;">-->
<!--              <div style="max-height: 150px; overflow-y: auto; background: #fef5e7; padding: 12px; border-radius: 4px;">-->
<!--                <pre-->
<!--                    style="margin: 0; white-space: pre-wrap; word-break: break-word; font-family: 'Courier New', monospace; font-size: 13px;">{{-->
<!--                    executionResult.error_message-->
<!--                  }}</pre>-->
<!--              </div>-->
<!--            </NCard>-->
<!--          </div>-->

          <!-- 响应内容（成功和失败都显示，只要有响应数据） -->
          <div v-if="executionResult.status_code || executionResult.response_body || executionResult.response_headers" style="flex: 1; min-height: 0;">
            <NTabs type="line" animated style="height: 100%;">
              <NTabPane name="body" tab="响应体">
                <div
                    style="height: 50vh; overflow-y: auto; border: 1px solid #e0e0e0; border-radius: 4px; background: #f8f9fa; position: relative;">
                  <NButton
                      quaternary
                      circle
                      size="small"
                      style="position: absolute; top: 8px; right: 8px; z-index: 1;"
                      @click="handleCopyResponse(executionResult.response_body)"
                  >
                    <TheIcon icon="material-symbols:content-copy" :size="16"/>
                  </NButton>
                  <pre
                      style="margin: 0; padding: 16px; white-space: pre-wrap; word-break: break-word; font-family: 'Courier New', monospace; font-size: 13px; line-height: 1.5;">{{
                      formatResponseBody(executionResult.response_body || '')
                    }}</pre>
                </div>
              </NTabPane>
              <NTabPane name="headers" tab="响应头">
                <div
                    style="height: 50vh; overflow-y: auto; border: 1px solid #e0e0e0; border-radius: 4px; background: #f8f9fa; position: relative;">
                  <NButton
                      quaternary
                      circle
                      size="small"
                      style="position: absolute; top: 8px; right: 8px; z-index: 1;"
                      @click="handleCopyResponse(executionResult.response_headers)"
                  >
                    <TheIcon icon="material-symbols:content-copy" :size="16"/>
                  </NButton>
                  <pre
                      style="margin: 0; padding: 16px; white-space: pre-wrap; word-break: break-word; font-family: 'Courier New', monospace; font-size: 13px; line-height: 1.5;">{{
                      JSON.stringify(executionResult.response_headers || {}, null, 2)
                    }}</pre>
                </div>
              </NTabPane>
              <NTabPane name="request" tab="请求信息">
                <div
                    style="height: 50vh; overflow-y: auto; border: 1px solid #e0e0e0; border-radius: 4px; background: #f8f9fa; position: relative;">
                  <NButton
                      quaternary
                      circle
                      size="small"
                      style="position: absolute; top: 8px; right: 8px; z-index: 1;"
                      @click="handleCopyResponse(executionResult.request_info)"
                  >
                    <TheIcon icon="material-symbols:content-copy" :size="16"/>
                  </NButton>
                  <pre
                      style="margin: 0; padding: 16px; white-space: pre-wrap; word-break: break-word; font-family: 'Courier New', monospace; font-size: 13px; line-height: 1.5;">{{
                      JSON.stringify(executionResult.request_info || {}, null, 2)
                    }}</pre>
                </div>
              </NTabPane>
              <NTabPane v-if="executionResult.assertion_results" name="assertions" tab="断言详情">
                <div
                    style="height: 50vh; overflow-y: auto; border: 1px solid #e0e0e0; border-radius: 4px; background: #f8f9fa; padding: 16px;">
                  <div v-if="executionResult.assertion_results.results.length === 0"
                       style="text-align: center; color: #999; padding: 40px;">
                    该测试用例未配置断言
                  </div>
                  <div v-else>
                    <div v-for="(result, index) in executionResult.assertion_results.results" :key="index"
                         style="border: 1px solid #e0e0e0; border-radius: 4px; padding: 12px; margin-bottom: 12px; background: white;">
                      <div
                          style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                        <span style="font-weight: 500;">断言 {{ index + 1 }}</span>
                        <NTag :type="result.passed ? 'success' : 'error'" size="small">
                          {{ result.passed ? '通过' : '失败' }}
                        </NTag>
                      </div>
                      <div style="margin-bottom: 4px;">
                        <span style="color: #666; font-size: 12px;">类型：</span>
                        <span>{{ getAssertionTypeLabel(result.type) }}</span>
                        <span style="color: #666; font-size: 12px; margin-left: 16px;">操作符：</span>
                        <span>{{ getAssertionOperatorLabel(result.type, result.operator) }}</span>
                      </div>
                      <div style="margin-bottom: 4px;">
                        <span style="color: #666; font-size: 12px;">期望值：</span>
                        <span
                            style="font-family: monospace; background: #f5f5f5; padding: 2px 4px; border-radius: 2px;">{{
                            result.expected_value
                          }}</span>
                      </div>
                      <div style="margin-bottom: 4px;">
                        <span style="color: #666; font-size: 12px;">实际值：</span>
                        <span
                            style="font-family: monospace; background: #f5f5f5; padding: 2px 4px; border-radius: 2px;">{{
                            result.actual_value
                          }}</span>
                      </div>
                      <div v-if="result.description" style="margin-bottom: 4px;">
                        <span style="color: #666; font-size: 12px;">描述：</span>
                        <span>{{ result.description }}</span>
                      </div>
                      <div v-if="!result.passed && result.error_message" style="color: #f56565; font-size: 12px;">
                        错误信息：{{ result.error_message }}
                      </div>
                    </div>
                  </div>
                </div>
              </NTabPane>
              <NTabPane v-if="executionResult.extracted_variables" name="variables" tab="变量提取">
                <div
                    style="height: 50vh; overflow-y: auto; border: 1px solid #e0e0e0; border-radius: 4px; background: #f8f9fa; padding: 16px;">
                  <div v-if="Object.keys(executionResult.extracted_variables || {}).length === 0"
                       style="text-align: center; color: #999; padding: 40px;">
                    该测试用例未配置变量提取或未提取到变量
                  </div>
                  <div v-else>
                    <div v-for="(value, name) in executionResult.extracted_variables" :key="name"
                         style="border: 1px solid #e0e0e0; border-radius: 4px; padding: 12px; margin-bottom: 12px; background: white;">
                      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                        <span style="font-weight: 500; color: #1890ff;">${{ name }}</span>
                        <NTag type="success" size="small">已提取</NTag>
                      </div>
                      <div style="margin-bottom: 4px;">
                        <span style="color: #666; font-size: 12px;">变量值：</span>
                        <span style="font-family: monospace; background: #f5f5f5; padding: 2px 4px; border-radius: 2px; word-break: break-all;">
                          {{ typeof value === 'object' ? JSON.stringify(value) : value }}
                        </span>
                      </div>
                      <div style="font-size: 12px; color: #52c41a;">
                        ✓ 该变量可在后续用例中使用 ${{ '{' + name + '}' }} 进行引用
                      </div>
                    </div>
                  </div>
                </div>
              </NTabPane>
            </NTabs>
          </div>
        </div>
        <div v-else style="text-align: center; padding: 40px; color: #999;">
          正在执行测试用例，请稍候...
        </div>
      </NSpin>
    </div>
  </NModal>

  <!-- 测试报告弹窗 -->
  <NModal
      v-model:show="testReportVisible"
      :mask-closable="false"
      preset="card"
      style="width: 90%; max-width: 1200px;"
      title="测试执行报告"
      size="huge"
      :bordered="false"
      :segmented="true"
  >
    <TestReport
        v-if="testReportData"
        :report-data="testReportData"
        :plan-name="planDetail.plan_name"
        :report-time="testReportData.report_time"
        @close="testReportVisible = false"
    />
  </NModal>

  <!-- 定时任务配置弹窗 -->
  <NModal
      v-model:show="scheduledTaskVisible"
      preset="dialog"
      :title="existingScheduledTask ? '编辑定时任务' : '创建定时任务'"
      :style="{ width: '700px' }"
      positive-text="确定"
      negative-text="取消"
      :positive-button-props="{ loading: scheduledTaskLoading }"
      @positive-click="handleSaveScheduledTask"
  >
    <NForm
        label-placement="left"
        label-align="left"
        :label-width="100"
        :model="scheduledTaskForm"
        style="margin-top: 16px;"
    >
      <NFormItem label="任务名称" path="task_name">
        <NInput
            v-model:value="scheduledTaskForm.task_name"
            clearable
            placeholder="请输入任务名称"
        />
      </NFormItem>

      <!-- 执行频率配置 -->
      <NFormItem label="执行频率">
        <NSpace vertical style="width: 100%;">
          <NRadioGroup v-model:value="cronConfig.type" @update:value="updateCronExpression">
            <NSpace>
              <NRadio
                  v-for="option in cronTypeOptions"
                  :key="option.value"
                  :value="option.value"
              >
                {{ option.label }}
              </NRadio>
            </NSpace>
          </NRadioGroup>

          <!-- 间隔设置 (每分钟和每小时时显示) -->
          <div v-if="cronConfig.type === 'minutely' || cronConfig.type === 'hourly'" style="margin-top: 12px;">
            <NSpace align="center">
              <span style="min-width: 60px;">间隔:</span>
              <span>每</span>
              <NInputNumber
                  v-model:value="cronConfig.interval"
                  :min="1"
                  :max="cronConfig.type === 'minutely' ? 59 : 23"
                  @update:value="updateCronExpression"
                  style="width: 80px;"
                  placeholder="1"
              />
              <span>{{ cronConfig.type === 'minutely' ? '分钟' : '小时' }}执行一次</span>
            </NSpace>
          </div>

          <!-- 执行时间 -->
          <div v-if="cronConfig.type !== 'custom' && cronConfig.type !== 'minutely' && cronConfig.type !== 'hourly'" style="margin-top: 12px;">
            <NSpace align="center">
              <span style="min-width: 60px;">执行时间:</span>
              <NSelect
                  v-model:value="cronConfig.time"
                  :options="timeOptions"
                  @update:value="updateCronExpression"
                  style="width: 120px;"
                  filterable
                  placeholder="选择时间"
              />
            </NSpace>
          </div>

          <!-- 周几执行 (仅周执行时显示) -->
          <div v-if="cronConfig.type === 'weekly'" style="margin-top: 12px;">
            <NSpace align="center">
              <span style="min-width: 60px;">执行日期:</span>
              <NCheckboxGroup v-model:value="cronConfig.weekdays" @update:value="updateCronExpression">
                <NSpace>
                  <NCheckbox
                      v-for="option in weekdayOptions"
                      :key="option.value"
                      :value="option.value"
                  >
                    {{ option.label }}
                  </NCheckbox>
                </NSpace>
              </NCheckboxGroup>
            </NSpace>
          </div>

          <!-- 每月几号执行 (仅月执行时显示) -->
          <div v-if="cronConfig.type === 'monthly'" style="margin-top: 12px;">
            <NSpace align="center">
              <span style="min-width: 60px;">执行日期:</span>
              <span>每月</span>
              <NInputNumber
                  v-model:value="cronConfig.dayOfMonth"
                  :min="1"
                  :max="31"
                  @update:value="updateCronExpression"
                  style="width: 80px;"
              />
              <span>日</span>
            </NSpace>
          </div>

          <!-- 执行描述 -->
          <div style="margin-top: 12px; padding: 8px 12px; background-color: #f5f5f5; border-radius: 4px; font-size: 13px; color: #666;">
            {{ getCronDescription() }}
          </div>
        </NSpace>
      </NFormItem>

      <NFormItem label="Cron表达式" path="cron_expression">
        <NInput
            v-model:value="scheduledTaskForm.cron_expression"
            :readonly="cronConfig.type !== 'custom'"
            clearable
            placeholder="自定义Cron表达式"
        />
        <template #feedback v-if="cronConfig.type === 'custom'">
          <div style="font-size: 12px; color: #999; margin-top: 4px;">
            <div>Cron表达式格式: 分 时 日 月 周</div>
            <div>• 0 9 * * 1-5 (工作日上午9点)</div>
            <div>• 0 */2 * * * (每2小时)</div>
            <div>• 0 0 * * * (每天午夜)</div>
          </div>
        </template>
      </NFormItem>

      <NFormItem label="任务描述" path="description">
        <NInput
            v-model:value="scheduledTaskForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入任务描述（可选）"
        />
      </NFormItem>
    </NForm>
  </NModal>
</template>

<style scoped>
.mr-5 {
  margin-right: 5px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-3 {
  margin-top: 12px;
}

.ml-4 {
  margin-left: 16px;
}

.detail-card {
  height: 100%;
}

.detail-info {
  padding: 16px 0;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  line-height: 1.5;
}

.detail-label {
  font-weight: 500;
  color: #666;
  min-width: 100px;
  margin-right: 8px;
  flex-shrink: 0;
}

.detail-value {
  font-weight: 500;
  color: #333;
  flex: 1;
}

.detail-description {
  max-width: 100%;
  word-break: break-word;
  white-space: pre-wrap;
  color: #333;
}

.case-count {
  font-size: 14px;
  color: #666;
  font-weight: normal;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

.text-gray {
  color: #999;
  font-size: 12px;
}

.modal-table-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e0e0e6;
  border-radius: 6px;
}

.simple-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.simple-table th {
  background-color: #fafafa;
  border: 1px solid #e0e0e6;
  padding: 8px 12px;
  text-align: left;
  font-weight: 500;
  color: #333;
}

.simple-table td {
  border: 1px solid #e0e0e6;
  padding: 8px 12px;
  vertical-align: middle;
}

.table-row:hover {
  background-color: #f5f5f5;
}

.url-cell {
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.debug-info {
  font-family: monospace;
  color: #666;
}

/* 断言配置样式 */
.assertion-container {
  width: 100%;
}

.assertion-item {
  margin-bottom: 12px;
  padding: 12px;
  border: 1px solid #e0e0e6;
  border-radius: 6px;
  background-color: #fafafa;
}

.assertion-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.assertion-description {
  margin-top: 8px;
}

/* 优化表格样式 */
:deep(.n-data-table-th) {
  background-color: #fafafa;
}

:deep(.n-data-table-td) {
  border-bottom: 1px solid #f0f0f0;
}
</style>
