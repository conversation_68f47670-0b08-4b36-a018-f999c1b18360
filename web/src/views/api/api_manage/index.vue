<script setup>
import { h, onMounted, ref, resolveDirective, withDirectives } from 'vue'
import {
  NButton,
  NForm,
  NFormItem,
  NInput,
  NSelect,
  NSwitch,
  NTag,
  NPopconfirm,
  NLayout,
  NLayoutSider,
  NLayoutContent,
  NTree,
  NDatePicker,
  NModal,
  NCard,
  NCode,
  NTabs,
  NTabPane,
  NSpin,
  NTooltip,
  useMessage,
} from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudModal from '@/components/table/CrudModal.vue'
import CrudTable from '@/components/table/CrudTable.vue'

import { formatDate, renderIcon } from '@/utils'
import { useCRUD } from '@/composables'
import api from '@/api'
import projectApi from '@/api/project'
import TheIcon from '@/components/icon/TheIcon.vue'

defineOptions({ name: 'API管理' })

// 在顶部添加 selectedKeys 响应式变量
const $table = ref(null)
const queryItems = ref({
  method: null,  // 需要明确初始化
  api_name: null,
  module_id: null  // 改为module_id
})
const selectedKeys = ref([])  // 新增：用于控制树的选中状态
const vPermission = resolveDirective('permission')
const $message = useMessage()

// 项目和模块相关状态
const projectOption = ref([])
const moduleOptions = ref([])
const selectedProjectId = ref(null)

// 执行结果弹窗相关状态
const executionModalVisible = ref(false)
const executionLoading = ref(false)
const executionResult = ref(null)

const methodOptions = [
  { label: 'GET', value: 'GET' },
  { label: 'POST', value: 'POST' },
  { label: 'PUT', value: 'PUT' },
  { label: 'DELETE', value: 'DELETE' },
  { label: 'PATCH', value: 'PATCH' },
]

const {
  modalVisible,
  modalTitle,
  modalAction,
  modalLoading,
  handleSave: originalHandleSave,  // 修改：保存原始的handleSave函数
  modalForm,
  modalFormRef,
  handleEdit: originalHandleEdit,
  handleDelete,
  handleAdd: originalHandleAdd,
} = useCRUD({
  name: 'API请求',
  initForm: {
    api_name: '',
    url: '',
    method: 'GET',
    project_id: '',
    module_id: null,
    params: '',
    headers: '',
    body: '',
    description: '',
    category: '',
    is_favorite: false,
    execution_count: 0,
  },
  doCreate: api.createApiRequest,
  doUpdate: api.updateApiRequest,
  doDelete: api.deleteApiRequest,
  refresh: () => $table.value?.handleSearch(),
})

// 新增：重写handleSave函数
const handleSave = async () => {
  try {
    // 调用原始保存函数并获取返回结果
    const result = await originalHandleSave();

    // 获取项目ID，优先使用返回结果中的项目ID，其次使用表单中的项目ID
    const projectId = result?.data?.project_id || modalForm.value.project_id;

    if (projectId) {
      // 更新选中状态和lastClickedProjectId
      lastClickedProjectId = projectId;
      selectedKeys.value = [projectId];
      selectedProjectId.value = projectId;

      // 确保模块选项已加载
      await loadModuleOptions(projectId);

      // 使用项目ID筛选接口列表
      queryItems.value.project_id = projectId;
      $table.value?.handleSearch();

      console.log('请求接口列表，项目ID:', projectId);
    } else {
      // 如果没有项目ID，则显示所有接口
      lastClickedProjectId = null;
      selectedKeys.value = [];
      selectedProjectId.value = null;
      delete queryItems.value.project_id;
      delete queryItems.value.module_id;
      moduleOptions.value = [];
      $table.value?.handleSearch();
    }
  } catch (error) {
    console.error('保存失败:', error);
    // 保存失败时也需要刷新表格
    $table.value?.handleSearch();
  }
}

// 重写handleEdit函数，确保project_id是字符串类型并加载模块选项
const handleEdit = async (row) => {
  const editRow = { ...row };
  if (editRow.project_id !== undefined && editRow.project_id !== null) {
    editRow.project_id = String(editRow.project_id);
    // 加载该项目的模块选项
    await loadModuleOptions(editRow.project_id);
  }
  originalHandleEdit(editRow);
}

// 重写handleAdd函数，设置默认项目ID
const handleAdd = async () => {
  originalHandleAdd();

  // 如果有选中的项目，设置为默认项目
  if (selectedProjectId.value) {
    modalForm.value.project_id = String(selectedProjectId.value);
    await loadModuleOptions(selectedProjectId.value);
  }
}

// 加载项目列表
const loadProjectList = async () => {
  try {
    const response = await projectApi.getProjectList()
    projectOption.value = response.data || []

    // 默认选中第一个项目
    if (projectOption.value.length > 0) {
      const firstProject = projectOption.value[0]
      selectedProjectId.value = firstProject.id
      selectedKeys.value = [firstProject.id]
      queryItems.value.project_id = firstProject.id

      // 加载第一个项目的模块
      await loadModuleOptions(firstProject.id)

      // 刷新表格数据
      $table.value?.handleSearch()
    }
  } catch (error) {
    console.error('加载项目列表失败:', error)
  }
}

// 加载模块选项
const loadModuleOptions = async (projectId) => {
  if (!projectId) {
    moduleOptions.value = []
    return
  }

  try {
    const response = await projectApi.getProjectModuleTree({ project_id: projectId })
    // 将树形结构转换为平铺的选项列表
    const flattenModules = (modules, result = []) => {
      modules.forEach(module => {
        result.push({
          label: module.name,
          value: module.id
        })
        if (module.children && module.children.length > 0) {
          flattenModules(module.children, result)
        }
      })
      return result
    }

    moduleOptions.value = flattenModules(response.data || [])
  } catch (error) {
    console.error('加载模块列表失败:', error)
    moduleOptions.value = []
  }
}

onMounted(() => {
  loadProjectList()
})

const columns = [
  {
    title: '接口名称',
    key: 'api_name',
    width: 180,
    align: 'left',
    ellipsis: { tooltip: true },
  },
  {
    title: '请求方法',
    key: 'method',
    width: 90,
    align: 'center',
    render(row) {
      const methodColorMap = {
        GET: 'success',
        POST: 'info',
        PUT: 'warning',
        DELETE: 'error',
        PATCH: 'default',
      }
      return h(
        NTag,
        { type: methodColorMap[row.method] || 'default', size: 'tiny' },
        { default: () => row.method }
      )
    },
  },
  {
    title: 'URL',
    key: 'url',
    width: 150,
    align: 'left',
    ellipsis: { tooltip: true },
    render(row) {
      const handleCopy = () => {
        navigator.clipboard.writeText(row.url).then(() => {
          $message.success('URL已复制到剪贴板');
        });
      };
      return h(
        'span',
        {
          style: 'cursor: pointer; color: #1890ff; text-decoration: underline;',
          onClick: handleCopy
        },
        row.url
      );
    }
  },
  {
    title: '请求体',
    key: 'body',
    width: 140,
    align: 'left',
    ellipsis: { tooltip: true },
    render(row) {
      if (!row.body) {
        return h('span', { style: 'color: #999; font-style: italic;' }, '无');
      }
      const handleCopy = () => {
        navigator.clipboard.writeText(row.body).then(() => {
          $message.success('请求体已复制到剪贴板');
        });
      };
      // 只显示一行内容，超出部分用省略号
      const bodyText = row.body.length > 30 ? row.body.substring(0, 30) + '...' : row.body;
      const displayText = bodyText.replace(/\n/g, ' ').replace(/\s+/g, ' ');
      return h(
        'span',
        {
          style: 'cursor: pointer; color: #1890ff; text-decoration: underline;',
          onClick: handleCopy
        },
        displayText
      );
    }
  },
  {
    title: '所属项目',
    key: 'project_id',
    width: 140,
    align: 'center',
    render(row) {
      const project = projectOption.value.find(p => p.id == row.project_id)
      return h('span', {}, project ? project.name : '-')
    },
  },
  {
    title: '所属模块',
    key: 'module_id',
    width: 120,
    align: 'center',
    render(row) {
      if (!row.module_id) {
        return h('span', { style: 'color: #999; font-style: italic;' }, '无')
      }
      const module = moduleOptions.value.find(m => m.value == row.module_id)
      return h('span', {}, module ? module.label : '-')
    },
  },
  {
    title: '执行次数',
    key: 'execution_count',
    width: 90,
    align: 'center',
    render(row) {
      return h(
        NTag,
        { type: row.execution_count > 0 ? 'info' : 'default', size: 'small' },
        { default: () => row.execution_count || 0 }
      )
    },
  },
  {
    title: '最后执行时间',
    key: 'last_executed',
    width: 150,
    align: 'center',
    render(row) {
      return h(
        'span',
        { style: 'font-size: 12px;' },
        row.last_executed ? formatDate(row.last_executed) : '未执行'
      )
    },
  },
  {
    title: '操作',
    key: 'actions',
    width: '180px',
    align: 'center',
    fixed: 'right',
    render(row) {
      return [
        withDirectives(
          h(
            NButton,
            {
              size: 'tiny',
              type: 'primary',
              style: 'margin-right: 4px;',
              onClick: () => {
                handleEdit(row)
              },
            },
            {
              default: () => '编辑',
              // icon: renderIcon('material-symbols:edit', { size: 16 }),
            }
          ),
          [[vPermission, 'post/api/v1/api_requests/update']]
        ),
        h(
          NPopconfirm,
          {
            onPositiveClick: () => handleDelete({ id: row.id }, false),
            onNegativeClick: () => {},
          },
          {
            trigger: () =>
              withDirectives(
                h(
                  NButton,
                  {
                    size: 'tiny',
                    type: 'error',
                    style: 'margin-right: 4px;',
                  },
                  {
                    default: () => '删除',
                    // icon: renderIcon('material-symbols:delete-outline', { size: 16 }),
                  }
                ),
                [[vPermission, 'delete/api/v1/api_requests/delete']]
              ),
            default: () => h('div', {}, '确定删除该API请求吗?'),
          }
        ),
        h(
          NButton,
          {
            size: 'tiny',
            type: 'info',
            onClick: () => handleExecuteApi(row),
          },
          {
            default: () => '执行',
            // icon: renderIcon('material-symbols:play-arrow', { size: 16 }),
          }
        ),
      ]
    },
  },
]

// 修改收藏状态
async function handleUpdateFavorite(row) {
  if (!row.id) return
  row.publishing = true
  row.is_favorite = !row.is_favorite
  try {
    await api.updateApiRequest(row)
    $message?.success(row.is_favorite ? '已添加到收藏' : '已取消收藏')
    $table.value?.handleSearch()
  } catch (err) {
    // 有异常恢复原来的状态
    row.is_favorite = !row.is_favorite
  } finally {
    row.publishing = false
  }
}

let lastClickedProjectId = null

// 修改nodeProps函数，更新选中状态
const nodeProps = ({ option }) => {
  return {
    async onClick() {
      if (lastClickedProjectId === option.id) {
        // 取消选择，显示所有项目的接口
        lastClickedProjectId = null
        selectedKeys.value = []
        selectedProjectId.value = null
        delete queryItems.value.project_id
        delete queryItems.value.module_id
        moduleOptions.value = []
        $table.value?.handleSearch()
      } else {
        // 选择项目
        lastClickedProjectId = option.id
        selectedKeys.value = [option.id]
        selectedProjectId.value = option.id
        queryItems.value.project_id = option.id
        delete queryItems.value.module_id  // 清除模块筛选

        // 加载该项目的模块
        await loadModuleOptions(option.id)

        // 刷新表格
        $table.value?.handleSearch()
      }
    },
  }
}

// 执行API请求
async function handleExecuteApi(row) {
  try {
    executionLoading.value = true
    executionModalVisible.value = true
    executionResult.value = null

    const response = await api.executeApiRequest({ api_request_id: row.id })

    if (response.code === 200) {
      executionResult.value = response.data
      // 刷新表格以更新执行次数和最后执行时间
      $table.value?.handleSearch()
    } else {
      $message.error(response.msg || 'API执行失败')
    }
  } catch (error) {
    console.error('执行API失败:', error)
    $message.error('执行API失败: ' + error.message)
  } finally {
    executionLoading.value = false
  }
}

// 关闭执行结果弹窗
function handleCloseExecutionModal() {
  executionModalVisible.value = false
  executionResult.value = null
}

// 格式化JSON字符串
function formatJson(jsonString) {
  try {
    const parsed = JSON.parse(jsonString)
    return JSON.stringify(parsed, null, 2)
  } catch (e) {
    return jsonString
  }
}

// 获取状态码颜色
function getStatusCodeType(statusCode) {
  if (statusCode >= 200 && statusCode < 300) return 'success'
  if (statusCode >= 300 && statusCode < 400) return 'warning'
  if (statusCode >= 400) return 'error'
  return 'default'
}

// 监听项目选择变化，加载对应模块
const handleProjectChange = async (projectId) => {
  modalForm.value.module_id = null  // 清空模块选择
  await loadModuleOptions(projectId)
}
// 复制响应体内容
const handleCopyResponse = async (content) => {
  try {
    await navigator.clipboard.writeText(JSON.stringify(content, null, 2));
    $message.success('复制成功');
  } catch (err) {
    $message.error('复制失败');
    console.error('复制失败:', err);
  }
};

const validateApiRequest = {
  api_name: [
    {
      required: true,
      message: '请输入接口名称',
      trigger: ['input', 'blur'],
    },
  ],
  url: [
    {
      required: true,
      message: '请输入请求URL',
      trigger: ['input', 'blur'],
    },
  ],
  method: [
    {
      required: true,
      message: '请选择请求方法',
      trigger: ['change', 'blur'],
    },
  ],
  project_id: [
    {
      required: true,
      message: '请选择所属项目',
      trigger: ['change', 'blur'],
    },
  ],
}
</script>

<template>
  <NLayout has-sider wh-full>
    <NLayoutSider
      bordered
      content-style="padding: 24px;"
      :collapsed-width="0"
      :width="240"
      show-trigger="arrow-circle"
    >
      <h1>项目列表</h1>
      <br />
      <NTree
        block-line
        :data="projectOption"
        key-field="id"
        label-field="name"
        default-expand-all
        :node-props="nodeProps"
        :selected-keys="selectedKeys"
      >
      </NTree>
    </NLayoutSider>
    <NLayoutContent>
      <CommonPage show-footer title="API请求列表">
        <template #action>
          <NButton v-permission="'post/api/v1/api_requests/create'" type="primary" @click="handleAdd">
            <TheIcon icon="material-symbols:add" :size="18" class="mr-5" />新建API请求
          </NButton>
        </template>
        <!-- 表格 -->
        <CrudTable
          ref="$table"
          v-model:query-items="queryItems"
          :columns="columns"
          :get-data="api.getApiRequestList"
        >
          <template #queryBar>
            <QueryBarItem label="接口名称" :label-width="70">
              <NInput
                v-model:value="queryItems.api_name"
                clearable
                type="text"
                placeholder="请输入接口名称"
                @keypress.enter="$table?.handleSearch()"
              />
            </QueryBarItem>
            <QueryBarItem label="请求方法" :label-width="70">
              <NSelect
                v-model:value="queryItems.method"
                clearable
                :options="methodOptions"
                placeholder="请选择请求方法"
                style="width: 100px" 
                @update:value="$table?.handleSearch()"
              />
            </QueryBarItem>
            <QueryBarItem label="所属模块" :label-width="70">
              <NSelect
                v-model:value="queryItems.module_id"
                clearable
                :options="moduleOptions"
                placeholder="请选择所属模块"
                style="width: 150px"
                @update:value="$table?.handleSearch()"
              />
            </QueryBarItem>
          </template>
        </CrudTable>

        <!-- 新增/编辑 弹窗 -->
        <CrudModal
          v-model:visible="modalVisible"
          :title="modalTitle"
          :loading="modalLoading"
          @save="handleSave"
        >
          <NForm
            ref="modalFormRef"
            label-placement="left"
            label-align="left"
            :label-width="100"
            :model="modalForm"
            :rules="validateApiRequest"
          >
            <NFormItem label="接口名称" path="api_name">
              <NInput v-model:value="modalForm.api_name" clearable placeholder="请输入接口名称" />
            </NFormItem>
            <NFormItem label="请求URL" path="url">
              <NInput v-model:value="modalForm.url" clearable placeholder="请输入请求URL" />
            </NFormItem>
            <NFormItem label="请求方法" path="method">
              <NSelect
                v-model:value="modalForm.method"
                :options="methodOptions"
                placeholder="请选择请求方法"
              />
            </NFormItem>
            <NFormItem label="所属项目" path="project_id">
              <NSelect
                v-model:value="modalForm.project_id"
                :options="projectOption.map(item => ({ label: item.name, value: String(item.id) }))"
                placeholder="请选择所属项目"
                @update:value="handleProjectChange"
              />
            </NFormItem>
            <NFormItem label="所属模块" path="module_id">
              <NSelect
                v-model:value="modalForm.module_id"
                :options="moduleOptions"
                placeholder="请选择所属模块（可选）"
                clearable
                :disabled="!modalForm.project_id"
              />
            </NFormItem>
            <NFormItem label="接口分类" path="category">
              <NInput v-model:value="modalForm.category" clearable placeholder="请输入接口分类" />
            </NFormItem>
            <NFormItem label="接口描述" path="description">
              <NInput v-model:value="modalForm.description" type="textarea" placeholder="请输入接口描述" />
            </NFormItem>
            <NFormItem label="请求参数" path="params">
              <NInput v-model:value="modalForm.params" type="textarea" placeholder="请输入请求参数(JSON格式)" />
            </NFormItem>
            <NFormItem label="请求头" path="headers">
              <NInput v-model:value="modalForm.headers" type="textarea" placeholder="请输入请求头(JSON格式)" />
            </NFormItem>
            <NFormItem label="请求体" path="body">
              <NInput v-model:value="modalForm.body" type="textarea" placeholder="请输入请求体" />
            </NFormItem>
            <NFormItem label="收藏" path="is_favorite">
              <NSwitch
                v-model:value="modalForm.is_favorite"
                size="small"
                :checked-value="true"
                :unchecked-value="false"
              ></NSwitch>
            </NFormItem>
          </NForm>
        </CrudModal>

        <!-- 执行结果弹窗 -->
        <NModal
          v-model:show="executionModalVisible"
          preset="card"
          title="API执行结果"
          style="width: 90%; max-width: 1200px;"
          :mask-closable="true"
          :closable="true"
          :auto-focus="false"
        >
          <div style="height: 70vh; overflow: hidden; display: flex; flex-direction: column;">
            <NSpin :show="executionLoading" style="flex: 1;">
              <div v-if="executionResult" style="height: 100%; display: flex; flex-direction: column;">
                <!-- 基本信息 -->
                <div style="margin-bottom: 16px; flex-shrink: 0;">
                  <NTag
                    :type="executionResult.success ? 'success' : 'error'"
                    style="margin-right: 8px;"
                    size="medium"
                  >
                    {{ executionResult.success ? '✓ 执行成功' : '✗ 执行失败' }}
                  </NTag>
                  <NTag
                    :type="getStatusCodeType(executionResult.status_code)"
                    style="margin-right: 8px;"
                    size="medium"
                  >
                    状态码: {{ executionResult.status_code || '无' }}
                  </NTag>
                  <NTag type="info" size="medium">
                    执行时间: {{ executionResult.execution_time }}ms
                  </NTag>
                </div>

                <!-- 错误信息 -->
                <div v-if="!executionResult.success && executionResult.error_message"
                     style="margin-bottom: 16px; flex-shrink: 0;">
                  <NCard title="错误信息" size="small" style="border: 1px solid #f56565;">
                    <div style="max-height: 150px; overflow-y: auto; background: #fef5e7; padding: 12px; border-radius: 4px;">
                      <pre style="margin: 0; white-space: pre-wrap; word-break: break-word; font-family: 'Courier New', monospace; font-size: 13px;">{{ executionResult.error_message }}</pre>
                    </div>
                  </NCard>
                </div>

                <!-- 响应内容 -->
                <div v-if="executionResult.success" style="flex: 1; min-height: 0;">
                  <NTabs type="line" animated style="height: 100%;">
                    <NTabPane name="body" tab="响应体">
                      <div style="height: 50vh; overflow-y: auto; border: 1px solid #e0e0e0; border-radius: 4px; background: #f8f9fa; position: relative;">
                        <NButton 
                          quaternary 
                          circle 
                          size="small" 
                          style="position: absolute; top: 8px; right: 8px; z-index: 1;" 
                          @click="handleCopyResponse(executionResult.response_body)"
                        >
                          <TheIcon icon="material-symbols:content-copy" :size="16" />
                        </NButton>
                        <pre style="margin: 0; padding: 16px; white-space: pre-wrap; word-break: break-word; font-family: 'Courier New', monospace; font-size: 13px; line-height: 1.5;">{{ formatJson(executionResult.response_body || '') }}</pre>
                      </div>
                    </NTabPane>
                    <NTabPane name="headers" tab="响应头">
                      <div style="height: 50vh; overflow-y: auto; border: 1px solid #e0e0e0; border-radius: 4px; background: #f8f9fa;">
                        <pre style="margin: 0; padding: 16px; white-space: pre-wrap; word-break: break-word; font-family: 'Courier New', monospace; font-size: 13px; line-height: 1.5;">{{ JSON.stringify(executionResult.response_headers || {}, null, 2) }}</pre>
                      </div>
                    </NTabPane>
                  </NTabs>
                </div>
              </div>
              <div v-else-if="!executionLoading" style="height: 100%; display: flex; align-items: center; justify-content: center;">
                <div style="text-align: center; color: #999;">
                  <p style="font-size: 16px; margin: 0;">暂无执行结果</p>
                </div>
              </div>
            </NSpin>
          </div>

          <template #footer>
            <div style="text-align: right;">
              <NButton @click="handleCloseExecutionModal" type="primary">关闭</NButton>
            </div>
          </template>
        </NModal>
      </CommonPage>
    </NLayoutContent>
  </NLayout>
</template>

<style scoped>
/* 表格样式优化 */
:deep(.n-data-table-th) {
  background-color: #fafafa;
  font-weight: 600;
}

:deep(.n-data-table-td) {
  padding: 8px 12px;
}

/* 标签样式优化 */
:deep(.n-tag) {
  border-radius: 4px;
  font-weight: 500;
}

/* 工具提示样式 */
:deep(.n-tooltip__content) {
  max-width: 500px;
  word-break: break-word;
}

/* 响应式布局 */
@media (max-width: 1200px) {
  :deep(.n-modal) {
    width: 95% !important;
  }
}

@media (max-width: 768px) {
  :deep(.n-modal) {
    width: 98% !important;
  }
}
</style>

